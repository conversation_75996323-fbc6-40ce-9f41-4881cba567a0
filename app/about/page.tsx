import { Users, Target, Shield, Zap } from 'lucide-react';

export default function About() {
  const values = [
    {
      icon: Target,
      title: 'Precision',
      description: 'Our AI algorithms are trained on vast datasets to make precise trading decisions.'
    },
    {
      icon: Shield,
      title: 'Security',
      description: 'Bank-grade security measures protect your funds and personal information.'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'Cutting-edge technology meets traditional trading wisdom for optimal results.'
    },
    {
      icon: Users,
      title: 'Community',
      description: 'Join thousands of traders who trust LightQuant for their crypto trading needs.'
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-bold text-slate-900 mb-6">
                About <span className="text-blue-600">LightQuant</span>
              </h1>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                We're revolutionizing crypto trading with AI-powered bots that learn, adapt, and optimize your trading strategy 24/7.
              </p>
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
                  Our Mission
                </h2>
                <p className="text-lg text-slate-600 mb-6">
                  To democratize advanced trading strategies and make sophisticated AI-powered trading accessible to everyone, from beginners to professional traders.
                </p>
                <p className="text-lg text-slate-600">
                  We believe that everyone should have access to the same powerful tools that institutional traders use, without the complexity or high barriers to entry.
                </p>
              </div>
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl h-80"></div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20 bg-slate-50">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                Our Values
              </h2>
              <p className="text-xl text-slate-600">
                The principles that guide everything we do
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <div key={index} className="bg-white rounded-xl p-8 text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <value.icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-4">{value.title}</h3>
                  <p className="text-slate-600">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
                Meet Our Team
              </h2>
              <p className="text-xl text-slate-600">
                Experienced professionals from finance, technology, and AI
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              {[1, 2, 3].map((member) => (
                <div key={member} className="text-center">
                  <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-6"></div>
                  <h3 className="text-xl font-bold text-slate-900 mb-2">Team Member {member}</h3>
                  <p className="text-blue-600 font-medium mb-4">Position Title</p>
                  <p className="text-slate-600">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>
    </>
  );
}
