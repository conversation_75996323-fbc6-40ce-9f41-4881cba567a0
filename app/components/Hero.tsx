import React from 'react';
import { ArrowR<PERSON>, TrendingUp, Shield, BarChart3 } from 'lucide-react';

const Hero = () => {
  const svgBackground = `data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234F46E5' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E`;

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 overflow-hidden">
      {/* Background Effects */}
      <div 
        className="absolute inset-0 opacity-40" 
        style={{ backgroundImage: `url("${svgBackground}")` }}
      ></div>
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute top-40 right-20 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-indigo-500/10 rounded-full blur-xl animate-pulse delay-2000"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 pt-32 pb-16">
        <div className="text-center">
          {/* Main Headline */}
          <h2 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight animate-fade-in-up">
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent animate-gradient">
              AI-Powered
            </span>
            <br />
            Crypto Trading Bots
          </h2>
          
          <p className="text-2xl md:text-3xl text-blue-100 mb-4 font-light animate-fade-in-up delay-200">
            Smarter. Faster. Fully Automated.
          </p>

          {/* Subheadline */}
          <p className="text-lg text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed animate-fade-in-up delay-300">
            Discover intelligent crypto trading bots trained on real market data.
            Optimize profits, reduce risk, and automate your strategy — all in one click.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up delay-500">
            <button className="group bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25">
              <span className="flex items-center gap-2">
                🚀 Start Free Trial – 7 Days
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </span>
            </button>
            <button className="border-2 border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105">
              📈 View Bot Performance
            </button>
          </div>

          {/* Key Benefits */}
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-500 animate-fade-in-up delay-700">
              <TrendingUp className="w-8 h-8 text-blue-400 mb-4 mx-auto" />
              <p className="text-white font-medium">🤖 AI-driven bots personalized to your risk profile</p>
            </div>
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-500 animate-fade-in-up delay-800">
              <Shield className="w-8 h-8 text-green-400 mb-4 mx-auto" />
              <p className="text-white font-medium">🔐 Secure API integration with Binance, Bybit, OKX</p>
            </div>
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-500 animate-fade-in-up delay-900">
              <BarChart3 className="w-8 h-8 text-purple-400 mb-4 mx-auto" />
              <p className="text-white font-medium">📊 Real-time analytics and transparent performance reports</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" className="w-full h-20 fill-slate-50">
          <path d="M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,48C672,43,768,53,864,58.7C960,64,1056,64,1152,58.7C1248,53,1344,43,1392,37.3L1440,32L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"></path>
        </svg>
      </div>
    </section>
  );
};

export default Hero;