import React from 'react';
import { TrendingUp, Shield, Activity } from 'lucide-react';

const FeaturedBots = () => {
  const bots = [
    {
      name: 'AlphaX',
      roi: '+27%',
      period: '30 days',
      strategy: 'Momentum + AI signals',
      icon: TrendingUp,
      color: 'from-green-500 to-emerald-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600'
    },
    {
      name: 'BetaShield',
      roi: '+15%',
      period: 'low drawdown',
      strategy: 'Futures, Risk-Controlled',
      icon: Shield,
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600'
    },
    {
      name: 'GammaQuant',
      roi: '+38%',
      period: 'sideways market',
      strategy: 'Mean-reversion + RL',
      icon: Activity,
      color: 'from-purple-500 to-pink-500',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600'
    }
  ];

  return (
    <section className="py-20 bg-slate-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
            🔥 Bots that Deliver Results
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            🧠 All bots are built by experts and powered by cutting-edge machine learning models.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {bots.map((bot, index) => {
            const Icon = bot.icon;
            return (
              <div
                key={index}
                className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <div className={`w-16 h-16 ${bot.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:rotate-6 transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${bot.textColor}`} />
                </div>
                
                <h3 className="text-2xl font-bold text-slate-900 mb-2">{bot.name}</h3>
                
                <div className="mb-4">
                  <span className={`text-3xl font-bold bg-gradient-to-r ${bot.color} bg-clip-text text-transparent`}>
                    {bot.roi}
                  </span>
                  <span className="text-slate-500 ml-2">ROI in the last {bot.period}</span>
                </div>
                
                <p className="text-slate-600 mb-6">
                  <span className="font-medium">Strategy:</span> {bot.strategy}
                </p>
                
                <div className="space-y-3">
                  <button className={`w-full bg-gradient-to-r ${bot.color} text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 group-hover:scale-105`}>
                    {index === 0 ? 'Follow Bot' : index === 1 ? 'View Details' : 'Copy Strategy'}
                  </button>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  );
};

export default FeaturedBots;