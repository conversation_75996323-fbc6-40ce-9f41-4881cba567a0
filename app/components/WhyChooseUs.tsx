import React from 'react';
import { <PERSON>, Shield, BarChart3, Zap, Network } from 'lucide-react';

const WhyChooseUs = () => {
  const features = [
    {
      icon: Brain,
      title: 'AI Optimization',
      description: 'Constantly improves strategy with market feedback',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      icon: Shield,
      title: 'Risk Engine',
      description: 'Adaptive capital allocation & stop-loss',
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    {
      icon: BarChart3,
      title: 'Backtest Transparency',
      description: 'All bots show full historical metrics',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    },
    {
      icon: Zap,
      title: 'Auto Deployment',
      description: 'One-click to follow, no code required',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50'
    },
    {
      icon: Network,
      title: 'Multi-exchange Support',
      description: 'Binance, Bybit, OKX, and more coming',
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50'
    }
  ];

  return (
    <section className="py-20 bg-slate-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
            🎯 Why Traders Trust Our Platform
          </h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.slice(0, 3).map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index}
                className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className={`w-16 h-16 ${feature.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:pulse transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${feature.color}`} />
                </div>
                
                <h3 className="text-xl font-bold text-slate-900 mb-3">
                  ✅ {feature.title}
                </h3>
                
                <p className="text-slate-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>

        <div className="grid md:grid-cols-2 gap-8 mt-8 max-w-4xl mx-auto">
          {features.slice(3).map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index + 3}
                className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2"
                style={{ animationDelay: `${(index + 3) * 100}ms` }}
              >
                <div className={`w-16 h-16 ${feature.bgColor} rounded-2xl flex items-center justify-center mb-6 group-hover:pulse transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${feature.color}`} />
                </div>
                
                <h3 className="text-xl font-bold text-slate-900 mb-3">
                  ✅ {feature.title}
                </h3>
                
                <p className="text-slate-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;