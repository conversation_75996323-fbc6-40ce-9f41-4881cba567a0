'use client';

import React, { useState } from 'react';
import { Search, User, ArrowRight, Tag, TrendingUp } from 'lucide-react';
import Link from 'next/link';

const BlogPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = [
    'All',
    '📚 Getting Started',
    '🤖 AI Trading Strategies', 
    '🛠️ Platform Tutorials',
    '🧠 Market Analysis',
    '📰 Product Updates'
  ];

  const featuredPost = {
    slug: 'how-we-built-alphax-ai-bot',
    title: 'How We Built AlphaX: Behind the AI That Beats the Market',
    excerpt: 'Discover how our data science team used Reinforcement Learning and market regime modeling to create AlphaX — our most followed and highest performing crypto bot.',
    image: 'https://images.pexels.com/photos/8369648/pexels-photo-8369648.jpeg?auto=compress&cs=tinysrgb&w=800&h=400&fit=crop',
    author: 'Dr. <PERSON>',
    date: '2025-01-15',
    category: '🤖 AI Trading Strategies',
    readTime: '8 min read',
    featured: true
  };

  const blogPosts = [
    {
      slug: 'bot-trading-beginners-guide',
      title: 'Bot Trading for Beginners: A 5-Minute Guide',
      excerpt: 'Curious about trading bots? Learn the basics of how automated strategies work, what risks to watch out for, and how to get started in under 5 minutes.',
      image: 'https://images.pexels.com/photos/6801648/pexels-photo-6801648.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      author: 'Mike Rodriguez',
      date: '2025-01-12',
      category: '📚 Getting Started',
      readTime: '5 min read'
    },
    {
      slug: 'backtest-to-real-trade-validation',
      title: 'From Backtest to Real Trade: How We Validate Every Bot',
      excerpt: 'Transparency is key. Here\'s how we take a strategy from historical simulation to live trading with performance tracking and built-in risk management.',
      image: 'https://images.pexels.com/photos/7567486/pexels-photo-7567486.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      author: 'Alex Thompson',
      date: '2025-01-10',
      category: '🛠️ Platform Tutorials',
      readTime: '7 min read'
    },
    {
      slug: 'ai-market-prediction-limitations',
      title: 'How AI Predicts Market Moves (And What It Gets Wrong)',
      excerpt: 'AI can detect patterns humans miss — but it\'s not perfect. Explore how we use ML models to capture alpha, and the safeguards we put in place to avoid overfitting.',
      image: 'https://images.pexels.com/photos/8369769/pexels-photo-8369769.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      author: 'Dr. Sarah Chen',
      date: '2025-01-08',
      category: '🧠 Market Analysis',
      readTime: '10 min read'
    },
    {
      slug: 'emotional-trading-vs-ai-bots',
      title: 'The Real Cost of Emotional Trading vs AI Bots',
      excerpt: 'Are your feelings costing you money? See how removing emotion from crypto trading through automation can lead to better long-term returns.',
      image: 'https://images.pexels.com/photos/7567443/pexels-photo-7567443.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      author: 'Emma Wilson',
      date: '2025-01-05',
      category: '🧠 Market Analysis',
      readTime: '6 min read'
    },
    {
      slug: 'platform-update-january-2025',
      title: 'Platform Update: New Risk Management Features',
      excerpt: 'Introducing advanced stop-loss mechanisms, portfolio rebalancing, and enhanced backtesting capabilities to help you trade smarter and safer.',
      image: 'https://images.pexels.com/photos/6801874/pexels-photo-6801874.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      author: 'LightQuant Team',
      date: '2025-01-03',
      category: '📰 Product Updates',
      readTime: '4 min read'
    },
    {
      slug: 'binance-integration-tutorial',
      title: 'Complete Guide: Setting Up Your Binance API',
      excerpt: 'Step-by-step tutorial on securely connecting your Binance account to LightQuant, including API key setup and security best practices.',
      image: 'https://images.pexels.com/photos/7567521/pexels-photo-7567521.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      author: 'Tech Support Team',
      date: '2025-01-01',
      category: '🛠️ Platform Tutorials',
      readTime: '12 min read'
    }
  ];

  const popularTags = ['#AITrading', '#BinanceBot', '#CryptoEducation', '#RiskManagement', '#Backtesting'];

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <>
      {/* Hero Section */}
      <section className="pt-12 pb-16 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in-up">
            Crypto Trading Insights, Bot Strategies & AI Tips
          </h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto animate-fade-in-up delay-200">
            Stay ahead of the market with in-depth articles on automated trading, AI strategies, platform updates, and more.
          </p>
        </div>
      </section>

      <div className="bg-slate-50 min-h-screen">
        <div className="max-w-7xl mx-auto px-6 py-16">
          
          {/* Featured Article */}
          <div className="mb-16">
            <div className="bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 group">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <img 
                    src={featuredPost.image} 
                    alt={featuredPost.title}
                    className="w-full h-64 md:h-full object-cover group-hover:scale-105 transition-transform duration-700"
                  />
                </div>
                <div className="md:w-1/2 p-8 md:p-12">
                  <div className="flex items-center gap-4 mb-4">
                    <span className="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                      Featured
                    </span>
                    <span className="text-slate-500 text-sm">{featuredPost.readTime}</span>
                  </div>
                  
                  <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4 group-hover:text-blue-600 transition-colors">
                    {featuredPost.title}
                  </h2>
                  
                  <p className="text-slate-600 mb-6 leading-relaxed">
                    {featuredPost.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-slate-900">{featuredPost.author}</p>
                        <p className="text-sm text-slate-500">{featuredPost.date}</p>
                      </div>
                    </div>
                    
                    <Link
                      href={`/blog/${featuredPost.slug}`}
                      className="group/btn bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 flex items-center gap-2"
                    >
                      Read Full Article
                      <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              
              {/* Filters */}
              <div className="mb-8">
                <div className="flex flex-wrap gap-3 mb-6">
                  {categories.map((category) => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                        selectedCategory === category
                          ? 'bg-blue-600 text-white shadow-lg'
                          : 'bg-white text-slate-600 hover:bg-blue-50 hover:text-blue-600'
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>

              {/* Blog Grid */}
              <div className="grid md:grid-cols-2 gap-8">
                {filteredPosts.map((post, index) => (
                  <article 
                    key={post.slug}
                    className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="relative overflow-hidden">
                      <img 
                        src={post.image} 
                        alt={post.title}
                        className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-700"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-white/90 backdrop-blur-sm text-slate-700 rounded-full text-xs font-medium">
                          {post.category}
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-slate-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                        {post.title}
                      </h3>
                      
                      <p className="text-slate-600 mb-4 line-clamp-3 leading-relaxed">
                        {post.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <User className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-slate-900">{post.author}</p>
                            <p className="text-xs text-slate-500">{post.date}</p>
                          </div>
                        </div>
                        <span className="text-sm text-slate-500">{post.readTime}</span>
                      </div>
                      
                      <Link
                        href={`/blog/${post.slug}`}
                        className="group/btn inline-flex items-center gap-2 text-blue-600 font-semibold hover:text-blue-700 transition-colors"
                      >
                        Read More
                        <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 space-y-8">
                
                {/* Search */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <h3 className="font-bold text-slate-900 mb-4 flex items-center gap-2">
                    <Search className="w-5 h-5 text-blue-600" />
                    Search Articles
                  </h3>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search blog posts..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                    <Search className="absolute right-3 top-3.5 w-4 h-4 text-slate-400" />
                  </div>
                </div>

                {/* Popular Tags */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <h3 className="font-bold text-slate-900 mb-4 flex items-center gap-2">
                    <Tag className="w-5 h-5 text-blue-600" />
                    Popular Tags
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {popularTags.map((tag) => (
                      <span 
                        key={tag}
                        className="px-3 py-1 bg-slate-100 text-slate-600 rounded-full text-sm hover:bg-blue-100 hover:text-blue-600 transition-colors cursor-pointer"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Recent Posts */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <h3 className="font-bold text-slate-900 mb-4 flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-blue-600" />
                    Recent Posts
                  </h3>
                  <div className="space-y-4">
                    {blogPosts.slice(0, 3).map((post) => (
                      <Link
                        key={post.slug}
                        href={`/blog/${post.slug}`}
                        className="block group"
                      >
                        <h4 className="font-medium text-slate-900 group-hover:text-blue-600 transition-colors line-clamp-2 mb-1">
                          {post.title}
                        </h4>
                        <p className="text-sm text-slate-500">{post.date}</p>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-center text-white">
            <h2 className="text-3xl font-bold mb-4">
              🚀 Ready to put what you've learned into action?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Start your 7-day free trial and let AI trade for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                Try a Bot Now
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                Browse All Bots
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogPage;